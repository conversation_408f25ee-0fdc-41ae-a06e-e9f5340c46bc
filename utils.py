import re
import unicodedata
from datetime import datetime
import os

import pandas as pd
from pdf2image import convert_from_path
import numpy as np


# Číselník pro automatickou klasifikaci typů hodnot (value_class)
VALUE_CLASS_REGISTRY = {
    0: "Neznámý typ",
    1: "<PERSON><PERSON>",
    2: "Procenta",
    3: "Desetinné číslo",
    4: "Číslo účtu",
    5: "Alfanumerický kód",
    6: "IBAN",
    7: "DIČ",
    8: "<PERSON><PERSON>lo"
}

# Číselník pro klasifikaci klíčů (key_class) - názvy listů z training_set.xlsx
KEY_CLASS_REGISTRY = {
    0: "Ostatní",
    1: "<PERSON>ak<PERSON>",
    2: "<PERSON><PERSON><PERSON>",
    3: "Zálohová faktura",
    4: "<PERSON><PERSON><PERSON>ha",
    5: "Č<PERSON>lo faktury",
    6: "Variabilní symbol",
    7: "Objednáv<PERSON>",
    8: "<PERSON><PERSON><PERSON> ob<PERSON>dn<PERSON>",
    9: "Dodavatel",
    10: "Odběratel",
    11: "Č<PERSON>lo účtu",
    12: "Datum vystavení",
    13: "Datum splatnosti",
    14: "DUZP",
    15: "Celkem k úhradě",
    16: "Základ DPH",
    17: "Sazba DPH",
    18: "IČO",
    19: "Číslo dodacího listu",
    20: "DIČ",
    21: "IBAN",
    22: "DPH"
}

# Číselník pro manuální klasifikaci kategorií faktury (result_class)
RESULT_CLASS_REGISTRY = {
    0: "Ostatní",
    1: "Číslo faktury",
    2: "Datum vystavení",
    3: "Datum splatnosti",
    4: "DUZP",
    5: "Číslo objednávky",
    6: "Variabilní symbol",
    7: "DIČ plátce",
    8: "IČO plátce",
    9: "DIČ dodavatele",
    10: "IČO dodavatele",
    11: "IBAN",
    12: "Číslo účtu",
    13: "Kód banky",
    14: "Sazba DPH",
    15: "Základ DPH",
    16: "Částka celkem s DPH",
    17: "Celková částka k úhradě",
    18: "Měna"
}


# Funkce pro práci s číselníky
def get_value_class_name(value_class_id):
    """Vrátí název pro value_class ID."""
    return VALUE_CLASS_REGISTRY.get(value_class_id, f"Neznámý typ {value_class_id}")


def get_key_class_name(key_class_id):
    """Vrátí název pro key_class ID."""
    return KEY_CLASS_REGISTRY.get(key_class_id, f"Neznámý klíč {key_class_id}")


def get_result_class_name(result_class_id):
    """Vrátí název pro result_class ID."""
    return RESULT_CLASS_REGISTRY.get(result_class_id, f"Neznámá kategorie {result_class_id}")


def get_available_result_classes():
    """Vrátí seznam dostupných kategorií pro dropdown jako (id, název)."""
    return [(class_id, class_name) for class_id, class_name in RESULT_CLASS_REGISTRY.items()]


def get_available_key_classes():
    """Vrátí seznam dostupných klíčů jako (id, název)."""
    return [(class_id, class_name) for class_id, class_name in KEY_CLASS_REGISTRY.items()]


def get_value_class_registry():
    """Vrátí kopii VALUE_CLASS_REGISTRY."""
    return VALUE_CLASS_REGISTRY.copy()


def get_key_class_registry():
    """Vrátí kopii KEY_CLASS_REGISTRY."""
    return KEY_CLASS_REGISTRY.copy()


def get_result_class_registry():
    """Vrátí kopii RESULT_CLASS_REGISTRY."""
    return RESULT_CLASS_REGISTRY.copy()


def load_key_class_mapping_from_xlsx(xlsx_path='training_data/training_set.xlsx'):
    """
    Načte názvy listů z Excel souboru a vytvoří mapování pro Classifier.

    Args:
        xlsx_path (str): Cesta k Excel souboru s tréninkovými daty.

    Returns:
        dict: Mapování názvů kategorií na číselné identifikátory pro Classifier.
    """
    import os

    if not os.path.exists(xlsx_path):
        print(f"Soubor {xlsx_path} nenalezen, používám výchozí mapování.")
        return {}

    try:
        xlsx = pd.ExcelFile(xlsx_path)
        sheet_names = xlsx.sheet_names

        # Vytvoříme mapování název_listu -> ID (začínáme od 1)
        mapping = {}
        for i, sheet_name in enumerate(sheet_names, 1):
            mapping[sheet_name] = i

        print(f"Načteno mapování pro {len(mapping)} kategorií klíčů z {xlsx_path}")
        return mapping

    except Exception as e:
        print(f"Chyba při načítání mapování z {xlsx_path}: {e}")
        return {}


def update_key_class_registry_from_xlsx(xlsx_path='training_data/training_set.xlsx'):
    """
    Aktualizuje KEY_CLASS_REGISTRY na základě názvů listů z Excel souboru.

    Args:
        xlsx_path (str): Cesta k Excel souboru s tréninkovými daty.

    Returns:
        bool: True pokud se aktualizace podařila, jinak False.
    """
    global KEY_CLASS_REGISTRY

    import os

    if not os.path.exists(xlsx_path):
        print(f"Soubor {xlsx_path} nenalezen, KEY_CLASS_REGISTRY zůstává nezměněn.")
        return False

    try:
        xlsx = pd.ExcelFile(xlsx_path)
        sheet_names = xlsx.sheet_names

        # Vytvoříme nový číselník s ID začínajícími od 1
        new_registry = {0: "Ostatní"}  # ID 0 vždy pro "Ostatní"
        for i, sheet_name in enumerate(sheet_names, 1):
            new_registry[i] = sheet_name

        # Aktualizujeme globální číselník
        KEY_CLASS_REGISTRY.clear()
        KEY_CLASS_REGISTRY.update(new_registry)

        print(f"KEY_CLASS_REGISTRY aktualizován s {len(sheet_names)} kategoriemi z {xlsx_path}")
        return True

    except Exception as e:
        print(f"Chyba při aktualizaci KEY_CLASS_REGISTRY z {xlsx_path}: {e}")
        return False


def contains_digits(text):
    return bool(re.search(r'\d', text))

def contains_letters(text):
    return any(unicodedata.category(char).startswith('L') for char in text)

def merge_texts(df):
    merged_rows = []
    buffer_texts = []
    buffer_left = None
    buffer_top = None
    buffer_right = None
    buffer_bottom = None

    def add_to_buffer(text, left, top, width, height):
        nonlocal buffer_texts, buffer_left, buffer_top, buffer_right, buffer_bottom
        if not buffer_texts:
            buffer_left = left
            buffer_top = top
            buffer_right = left + width
            buffer_bottom = top + height
        buffer_texts.append(text)
        buffer_right = max(buffer_right, left + width)
        buffer_bottom = max(buffer_bottom, top + height)

    def flush_buffer():
        nonlocal buffer_texts, buffer_left, buffer_top, buffer_right, buffer_bottom
        if buffer_texts:
            merged_text = ' '.join(buffer_texts)
            merged_width = buffer_right - buffer_left
            merged_height = buffer_bottom - buffer_top
            merged_rows.append([merged_text, buffer_left, buffer_top, merged_width, merged_height, None])
        buffer_texts = []
        buffer_left = buffer_top = buffer_right = buffer_bottom = None

    for i in range(len(df)):
        current_text = df.loc[i, 'text']
        current_left = df.loc[i, 'left']
        current_top = df.loc[i, 'top']
        current_width = df.loc[i, 'width']
        current_height = df.loc[i, 'height']

        # Kontrola, jestli se jedná o poslední prvek
        if i < len(df) - 1:
            next_text = df.loc[i + 1, 'text']
            next_left = df.loc[i + 1, 'left']
            next_top = df.loc[i + 1, 'top']
            next_width = df.loc[i + 1, 'width']
            next_height = df.loc[i + 1, 'height']

            # Vzdálenost mezi dvěma texty na ose x
            distance = next_left - (current_left + current_width)

            # Podmínky pro spojení textů
            current_is_digit = current_text.isdigit()
            next_is_digit = next_text.isdigit()
            current_has_letters = contains_letters(current_text)
            next_has_letters = contains_letters(next_text)

            should_merge = (
                    0 < distance < current_height * 1.5 and
                    not current_text.endswith((':', ';')) and
                    not (current_is_digit and next_has_letters) and
                    not (current_has_letters and next_is_digit)
            )

            if should_merge:
                add_to_buffer(current_text, current_left, current_top, current_width, current_height)
                continue

        # Přidání aktuálního textu do bufferu a flush
        add_to_buffer(current_text, current_left, current_top, current_width, current_height)
        flush_buffer()

    # Poslední flush, pokud něco zůstalo
    flush_buffer()

    # Vytvoření nového dataframe
    merged_df = pd.DataFrame(merged_rows, columns=['text', 'left', 'top', 'width', 'height', 'class'])
    return merged_df

def clean_texts(df, text_column='text'):
    def clean_text(text):
        # # Odstranění diakritiky
        # text = unicodedata.normalize('NFD', text)
        # text = ''.join(char for char in text if not unicodedata.combining(char))

        # Odstranění všech znaků s kódem > 255
        #text = ''.join(char for char in text if ord(char) < 256)

        # Nahrazení měnových symbolů
        text = re.sub(r'Kč', 'CZK', text)
        text = re.sub(r'€', 'EUR', text)
        text = re.sub(r'\$', 'USD', text)
        text = re.sub(r'£', 'GBP', text)
        text = re.sub(r'¥', 'JPY', text)

        # Odstranění zdvojených mezer
        text = unicodedata.normalize("NFC", text)  # Normalize Unicode
        text = text.lower().strip()  # Lowercase
        text = re.sub(r'(?<=\w)\.(?=\w)', ' ', text)  # Replace dots between letters with space
        text = re.sub(r'[.,:;]', '', text)  # Remove remaining punctuation
        text = re.sub(r'\s+', ' ', text).strip()  # Collapse multiple spaces

        return text.strip()

    # Aplikace funkce na každý text
    df[text_column] = df[text_column].apply(clean_text)

    # Odstranění řádků s textem kratším než 3 znaky, kromě "ič" a "ic"
    initial_count = len(df)
    df = df[
        (df[text_column].str.len() >= 3) |
        (df[text_column].isin(['ič', 'ic']))
    ].copy()

    removed_count = initial_count - len(df)
    if removed_count > 0:
        print(f"Odstraněno {removed_count} řádků s textem kratším než 3 znaky (kromě 'ič' a 'ic')")

    return df

def classify_values(text):
    """Klasifikuje jednotlivý textový řetězec"""
    # 1. Kontrola data
    # r'^(0[1-9]|[12][0-9]|3[01])[-/.](0[1-9]|1[0-2])[-/.](\d{4})$'
    date_formats = ["%d.%m.%Y", "%Y-%m-%d", "%d/%m/%Y", "%d-%m-%Y"]
    for fmt in date_formats:
        try:
            datetime.strptime(text, fmt)
            return 1  # "Datum"
        except ValueError:
            pass

    # 2. Procenta
    if re.match(r'^\s*\d+([.,]\d+)?\s*%\s*$', text):
        return 2  # "Procenta"

    # 3. Desetinné číslo
    if re.match(r'^\d{1,3}(?:[.,\s]\d{3})*(?:[.,]\d+)$', text):
        return 3  # "Desetinné číslo"

    # 4. Číslo účtu
    if re.match(r'^(?:\d{2,6}-)?\d{3,10}/\d{4}$', text):
        return 4

        # 6. IBAN
    if re.match(r'^[A-Z]{2}[\s-]?(\d{2}[\s-]?([A-Z0-9]{4}[\s-]?){5,7})$', text):
        return 6

    # 7. DIČ
    if re.match(r'^(ATU|BE|BG|CY|CZ|DE|DK|EE|ES|FI|FR|GR|HR|HU|IE|IT|LT|LU|LV|MT|NL|PL|PT|RO|SE|SI|SK)\s?\d{8,12}$',
                text):
        return 7

    # 8. Number
    if re.match(r'^\d{1,20}$', text):
        return 8

        # 5. Alfanumerický kód
    if re.match(r'^(?=.*\d)[A-Za-z0-9./-]+$', text):
        return 5  # "Alfanumerický kód"

    return 0  # "Neznámý typ"


def classify_batch_values(df):
    """Zpracuje celý DataFrame a přidá sloupec s klasifikací"""
    # df = df.copy()
    df['value_class'] = df['text'].apply(classify_values)

    return df


def get_page_dimensions(file_path):
    """
    Získá rozměry stránky z PDF souboru.

    Args:
        file_path (str): Cesta k PDF souboru

    Returns:
        tuple: (page_width, page_height) v pixelech
    """
    try:
        # Načteme první stránku pro získání rozměrů
        images = convert_from_path(file_path, dpi=300, first_page=1, last_page=1)
        if images:
            image = np.array(images[0])
            height, width = image.shape[:2]
            return width, height
        else:
            raise ValueError("Nelze načíst stránku z PDF")
    except Exception as e:
        print(f"Chyba při získávání rozměrů stránky: {e}")
        # Výchozí rozměry pokud se nepodaří načíst
        return 800, 1200


def ensure_results_directory():
    """Zajistí existenci složky Results."""
    results_dir = "Results"
    if not os.path.exists(results_dir):
        os.makedirs(results_dir)
        print(f"Vytvořena složka: {results_dir}")
    return results_dir


def get_output_filename(file_path):
    """
    Vytvoří název výstupního CSV souboru na základě názvu vstupního PDF.

    Args:
        file_path (str): Cesta k vstupnímu PDF souboru

    Returns:
        str: Cesta k výstupnímu CSV souboru ve složce Results
    """
    # Získáme název souboru bez přípony
    base_name = os.path.splitext(os.path.basename(file_path))[0]

    # Zajistíme existenci složky Results
    results_dir = ensure_results_directory()

    # Vytvoříme cestu k výstupnímu souboru
    output_file = os.path.join(results_dir, f"{base_name}.csv")

    return output_file


def export_filtered_results(df, file_path):
    """
    Exportuje filtrované výsledky do CSV souboru.

    Exportují se pouze řádky kde key_class > 0 a result_class > 0.
    Na konec se přidá řádek "page" s bounding boxem celé stránky.

    Args:
        df (pandas.DataFrame): DataFrame s výsledky
        file_path (str): Cesta k původnímu PDF souboru

    Returns:
        str: Cesta k vytvořenému CSV souboru
    """
    # Filtrujeme data podle podmínek
    filtered_df = df[(df['key_class'] > 0) | (df['result_class'] > 0)].copy()

    print(f"Filtrování dat:")
    print(f"  Celkem řádků: {len(df)}")
    print(f"  key_class > 0: {len(df[df['key_class'] > 0])}")
    print(f"  result_class > 0: {len(df[df['result_class'] > 0])}")
    print(f"  Splňuje obě podmínky: {len(filtered_df)}")

    # Získáme rozměry stránky
    page_width, page_height = get_page_dimensions(file_path)

    # Přidáme řádek "page"
    page_row = {
        'text': 'page',
        'left': 0,
        'top': 0,
        'width': page_width,
        'height': page_height,
        'key_class': 0,
        'value_class': 0,
        'result_class': 0
    }

    # Přidáme další sloupce pokud existují
    for col in df.columns:
        if col not in page_row:
            page_row[col] = 0 if df[col].dtype in ['int64', 'float64'] else None

    # Přidáme řádek page na konec
    page_df = pd.DataFrame([page_row])
    final_df = pd.concat([filtered_df, page_df], ignore_index=True)

    # Získáme název výstupního souboru
    output_file = get_output_filename(file_path)

    # Uložíme do CSV
    final_df.to_csv(output_file, index=False)

    print(f"Export dokončen:")
    print(f"  Exportováno řádků: {len(final_df)} (včetně řádku 'page')")
    print(f"  Soubor uložen: {output_file}")

    return output_file
